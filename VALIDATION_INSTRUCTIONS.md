# AI Utility Orchestrator - Installation Validation

## For Users Who Want to Test the Package

If you've installed the `ai_utility_orchestrator` package and want to validate that everything is working correctly, follow these steps:

### Method 1: Quick Validation (No API Key Required)

1. **Download the validation script:**
   ```bash
   # Download the standalone validation script
   curl -O https://raw.githubusercontent.com/your-repo/ai_utility_orchestrator/main/validate_ai_utility_orchestrator.py

   # Or manually download from the repository
   ```

2. **Run the validation:**
   ```bash
   python validate_ai_utility_orchestrator.py
   ```

### Method 2: Real-World Scenario Test (API Key Required) ⭐

For complete validation with actual OpenAI API calls:

1. **Download the real-world test script:**
   ```bash
   curl -O https://raw.githubusercontent.com/your-repo/ai_utility_orchestrator/main/test_real_world_scenario.py
   ```

2. **Set your OpenAI API key:**
   ```bash
   export OPENAI_API_KEY="your-actual-api-key-here"
   ```

3. **Run the real-world test:**
   ```bash
   python test_real_world_scenario.py
   ```

   **Note:** This test makes actual API calls and may cost ~$0.01-0.05 USD

3. **Expected output:**
   ```
   ============================================================
    AI Utility Orchestrator - Installation Validation
   ============================================================
   This script validates that the AI Utility Orchestrator package
   is properly installed and functioning correctly.

   🔄 Testing package imports... ✅ PASSED
   🔄 Testing tool system... ✅ PASSED
   🔄 Testing configuration system... ✅ PASSED
   🔄 Testing context management... ✅ PASSED
   🔄 Testing response formatting... ✅ PASSED
   🔄 Testing agent executor... ✅ PASSED

   ============================================================
    Validation Summary
   ============================================================
   Tests passed: 6/6
   🎉 SUCCESS: AI Utility Orchestrator is properly installed and working!

   You can now use the package in your projects:
      from ai_utility_orchestrator import agent_executor
      result = agent_executor('Your query here')
   ```

### Method 3: Use Built-in Tests (For Developers)

If you want to run the comprehensive test suite that comes with the package:

1. **Install with test dependencies:**
   ```bash
   pip install ai_utility_orchestrator[test]
   ```

2. **Run the tests:**
   ```bash
   pytest -v
   ```

## What Each Validation Test Does

### Quick Validation Tests (No API Key):

### 1. **Package Imports Test**
- Verifies all main components can be imported
- Checks package version information
- Ensures no import errors

### 2. **Tool System Test**
- Tests tool creation and registration
- Validates tool execution
- Checks registry functionality

### 3. **Configuration System Test**
- Tests default configuration loading
- Validates custom configuration files
- Checks configuration merging

### 4. **Context Management Test**
- Tests conversation history storage
- Validates context retrieval
- Checks message formatting

### 5. **Response Formatting Test**
- Tests LLM response handling (with mocking)
- Validates JSON parsing
- Checks metadata handling

### 6. **Agent Executor Test**
- Tests the main orchestration function
- Validates both direct responses and tool execution
- Checks complete workflow

### Real-World Scenario Tests (API Key Required):

### 1. **Direct Response Test**
- Makes actual OpenAI API calls
- Tests responses without tools
- Validates real LLM integration

### 2. **Custom Tools Test**
- Tests calculator and text analyzer tools
- Validates tool selection by real AI
- Checks tool execution and response formatting

### 3. **Conversation Context Test**
- Tests multi-turn conversations
- Validates context persistence across interactions
- Ensures AI remembers previous conversation

### 4. **Error Handling Test**
- Tests graceful handling of broken tools
- Validates error recovery mechanisms
- Ensures system stability

### 5. **Configuration Loading Test**
- Tests loading custom configurations from files
- Validates configuration merging and overrides
- Ensures flexibility in setup

## Troubleshooting

### If validation fails:

1. **Check installation:**
   ```bash
   pip list | grep ai-utility-orchestrator
   ```

2. **Reinstall the package:**
   ```bash
   pip uninstall ai_utility_orchestrator
   pip install ai_utility_orchestrator
   ```

3. **Check Python version:**
   - Requires Python 3.8 or higher

4. **Check dependencies:**
   ```bash
   pip install openai python-dotenv PyYAML
   ```

### Common Issues:

- **Import errors**: Package not properly installed
- **API key warnings**: Normal for validation (uses mocking)
- **Permission errors**: Check file system permissions for context storage

## Need Help?

If you encounter issues:
1. Check the error messages in the validation output
2. Ensure you have the required Python version (3.8+)
3. Try reinstalling the package
4. Contact the package maintainer with the validation output

## What's Next?

Once validation passes, you can start using the package:

```python
from ai_utility_orchestrator import agent_executor

# Set your OpenAI API key
import os
os.environ['OPENAI_API_KEY'] = 'your-api-key-here'

# Use the agent
result = agent_executor("What's the weather like today?")
print(result['final_response'])
```

For more examples and documentation, check the main README.md file.
