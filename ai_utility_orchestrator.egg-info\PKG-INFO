Metadata-Version: 2.4
Name: ai_utility_orchestrator
Version: 0.1.0
Summary: A universal, AI-powered utility orchestration package with dynamic LLM-driven routing.
Author: <PERSON><PERSON><PERSON>
License: MIT
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: openai
Requires-Dist: python-dotenv
Requires-Dist: PyYAML
Requires-Dist: importlib-resources>=1.3.0; python_version < "3.9"
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: pytest-mock>=3.10.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.0.0; extra == "test"
Requires-Dist: pytest-cov>=4.0.0; extra == "test"
Requires-Dist: pytest-mock>=3.10.0; extra == "test"

# AI Utility Orchestrator

AI Utility Orchestrator is a universal, AI-powered orchestration framework that dynamically routes user queries to the most appropriate tool or agent using Large Language Models (LLMs), a pluggable registry, and context-aware session management.

## Features

- LLM-powered dynamic tool/agent selection
- Pluggable agent and tool registry (no hardcoding)
- Context management across sessions
- Modular response formatting
- Domain-agnostic and reusable across projects
- Clean and extensible architecture

## Installation

### From Source

Clone the repository and install:

```bash
git clone https://github.com/your-org/ai_utility_orchestrator.git
cd ai_utility_orchestrator

# Install as a package
pip install .

# For development with testing dependencies
pip install -e ".[dev]"
```

### From PyPI (when published)

```bash
pip install ai-utility-orchestrator
```

## Usage

```python
from ai_utility_orchestrator import agent_executor, ConfigUtils

# Load configuration
config = ConfigUtils.load_config()

# Execute a query
result = agent_executor("What tools are available?", config=config)
print(result['final_response'])

# Example with custom user ID
result = agent_executor(
    "Search for Python tutorials",
    config=config,
    user_id="user123"
)
print(f"Response: {result['final_response']}")
print(f"Tool used: {result['selected_tool']}")
```

## Configuration

The package includes a default configuration file. You can customize it by:

1. Creating a custom config file based on `config/config_default.json`
2. Setting the `AI_ORCHESTRATOR_CONFIG` environment variable
3. Passing a config path to `ConfigUtils.load_config()`

## Data Storage

- Chat history is stored in `~/.ai_utility_orchestrator/chat_history.json`
- You can customize the storage location in the configuration

## Testing

After installing the package, you can test the entire functionality with:

```bash
# Install with test dependencies
pip install ai_utility_orchestrator[test]

# Run the comprehensive test suite
pytest -v

# Or run the specific test file
pytest tests/test_ai_utility_orchestrator.py -v
```

The test suite includes:
- Package import validation
- Tool creation and registry functionality
- Configuration loading and management
- Context management and persistence
- Response formatting and LLM integration
- Complete end-to-end workflow testing
- Error handling scenarios

All tests use mocking to avoid external API dependencies, so they can run offline.
