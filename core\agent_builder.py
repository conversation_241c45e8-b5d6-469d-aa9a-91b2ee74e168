# ai_utility_orchestrator/core/agent_builder.py

import logging
import json
import re
from typing import Dict, Any, Optional
from ai_utility_orchestrator.utils.response_formatter import format_response
from ai_utility_orchestrator.utils.toolkit import ConfigUtils
from ai_utility_orchestrator.core.agent_registry import ToolRegistry
from ai_utility_orchestrator.core.tools import Tool
from ai_utility_orchestrator.utils.context_manager import ContextManager

# Setup logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def _build_tool_selection_prompt(user_input: str, tools: list, context_messages: list = None, config: dict = None) -> str:
    """Build a prompt that asks the LLM to select appropriate tools."""

    # Build tool descriptions
    tool_descriptions = []
    for tool in tools:
        schema_str = json.dumps(tool.schema, indent=2) if tool.schema else "No parameters required"
        tool_descriptions.append(f"- {tool.name}: {tool.description}\n  Schema: {schema_str}")

    tools_text = "\n".join(tool_descriptions)

    # Include context if available
    context_text = ""
    if context_messages:
        context_text = "\n\nPrevious conversation context is available for reference."

    # Get configurable prompt template
    if config and "prompt_templates" in config and "tool_selection" in config["prompt_templates"]:
        template = config["prompt_templates"]["tool_selection"]
        prompt = template.format(
            tools_text=tools_text,
            user_input=user_input,
            context_text=context_text
        )
    else:
        # Default template with strict JSON formatting instructions
        prompt = f"""You are an AI Orchestrator. Analyze the user's request and determine which tool to use.

Available tools:
{tools_text}

User request: {user_input}{context_text}

IMPORTANT: You MUST respond with ONLY a valid JSON object. No additional text, explanations, or formatting.

Required JSON format:
{{"selected_tool": "tool_name_or_none", "parameters": {{}}, "reasoning": "brief_explanation", "direct_response": "answer_if_no_tool_needed"}}

Examples:
{{"selected_tool": "search_tool", "parameters": {{"query": "Python tutorials"}}, "reasoning": "User wants to search for information", "direct_response": ""}}

{{"selected_tool": "none", "parameters": {{}}, "reasoning": "General question I can answer directly", "direct_response": "Your answer here"}}

Respond with JSON only:"""

    return prompt


def _enhance_tool_parameters(params: Dict[str, Any], tool: 'Tool', config: dict) -> Dict[str, Any]:
    """Allow AI to enhance tool parameters based on context."""
    try:
        enhancement_prompt = f"""
You are helping to optimize tool parameters.

Tool: {tool.name}
Description: {tool.description}
Current parameters: {json.dumps(params, indent=2)}
Original user query: {params.get('original_query', 'N/A')}

Based on the user's query and tool capabilities, suggest any parameter enhancements or additions that would improve the tool's effectiveness. Return the enhanced parameters as JSON.

Only modify parameters that would genuinely improve the result. If no enhancements are needed, return the original parameters.
"""

        # Configurable temperature for parameter enhancement
        enhancement_temp = config.get("parameter_enhancement", {}).get("temperature", 0.3)

        result = format_response(
            prompt=enhancement_prompt,
            formatter="json",
            model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
            temperature=enhancement_temp,
            return_meta=True
        )

        if result and not result.get("error"):
            enhanced = result.get("parsed_response", params)
            if isinstance(enhanced, dict):
                logger.info(f"🔧 Enhanced tool parameters: {enhanced}")
                return enhanced
    except Exception as e:
        logger.warning(f"Parameter enhancement failed: {e}")

    return params


def _extract_tool_decision(llm_response: str, config: dict = None) -> Dict[str, Any]:
    """Extract tool selection decision from LLM response with robust error handling."""
    logger.info(f"🔍 Processing LLM response: {llm_response[:200]}...")

    if not llm_response or not llm_response.strip():
        logger.warning("Empty LLM response received")
        return {
            "selected_tool": "none",
            "parameters": {},
            "reasoning": "Empty response from LLM",
            "direct_response": "I didn't receive a proper response. Please try again."
        }

    cleaned_response = llm_response.strip()

    # Get configurable JSON extraction patterns
    extraction_config = config.get("json_extraction", {}) if config else {}
    patterns = extraction_config.get("patterns", [
        r'\{.*?\}',  # Default: any JSON object
        r'```json\s*(\{.*?\})\s*```',  # JSON in code blocks
        r'\{[^{}]*"selected_tool"[^{}]*\}',  # Tool selection specific
    ])

    # Build dynamic extraction methods based on config
    json_extraction_methods = []

    # Method 1: Direct JSON parsing (always first)
    json_extraction_methods.append(
        lambda text: json.loads(text) if text.startswith('{') and text.endswith('}') else None
    )

    # Dynamic methods based on patterns
    for pattern in patterns:
        if 'group(1)' in pattern or '```' in pattern:
            # Pattern with capture group
            json_extraction_methods.append(
                lambda text, p=pattern: json.loads(re.search(p, text, re.DOTALL).group(1)) if re.search(p, text, re.DOTALL) else None
            )
        else:
            # Pattern without capture group
            json_extraction_methods.append(
                lambda text, p=pattern: json.loads(re.search(p, text, re.DOTALL).group()) if re.search(p, text, re.DOTALL) else None
            )

    # Try each extraction method
    for i, method in enumerate(json_extraction_methods):
        try:
            result = method(cleaned_response)
            if result and isinstance(result, dict) and "selected_tool" in result:
                logger.info(f"JSON extracted using method {i+1}: {result}")
                return result
        except (json.JSONDecodeError, AttributeError, TypeError) as e:
            logger.debug(f"Method {i+1} failed: {e}")
            continue

    # If all JSON extraction fails, try to parse manually
    logger.warning("All JSON extraction methods failed, attempting manual parsing")

    # Look for key-value patterns
    tool_match = re.search(r'"selected_tool"\s*:\s*"([^"]*)"', cleaned_response)
    params_match = re.search(r'"parameters"\s*:\s*(\{[^}]*\})', cleaned_response)
    reasoning_match = re.search(r'"reasoning"\s*:\s*"([^"]*)"', cleaned_response)
    direct_match = re.search(r'"direct_response"\s*:\s*"([^"]*)"', cleaned_response)

    if tool_match:
        logger.info("Manual parsing successful")
        return {
            "selected_tool": tool_match.group(1),
            "parameters": json.loads(params_match.group(1)) if params_match else {},
            "reasoning": reasoning_match.group(1) if reasoning_match else "Manual parsing",
            "direct_response": direct_match.group(1) if direct_match else cleaned_response
        }

    # Final fallback - treat as direct response
    logger.warning(f"Could not parse response, treating as direct answer: {cleaned_response[:100]}...")
    return {
        "selected_tool": "none",
        "parameters": {},
        "reasoning": "Could not parse LLM response format",
        "direct_response": cleaned_response
    }


def agent_executor(user_input: str, config=None, user_id: str = None):
    """
    Main orchestration function that uses AI to select and execute tools.

    Args:
        user_input: The user's query
        config: Configuration dictionary (optional)
        user_id: User identifier for context management
    """
    # Configurable logging messages
    log_messages = config.get("log_messages", {}) if config else {}
    start_msg = log_messages.get("start", "Starting agent_executor...")
    logger.info(start_msg)

    # Load config if not provided
    if config is None:
        config = ConfigUtils.load_config()

    # Extract LLM configuration
    model_name = config.get("llm", {}).get("model", "gpt-4o-mini")
    temperature = config.get("llm", {}).get("temperature", 0.7)
    system_prompt = config.get("system_prompt", "You are an AI Orchestrator that helps users by routing queries to appropriate tools.")

    # Get user_id from config if not provided
    if user_id is None:
        user_id = config.get("default_user_id", "default_user")

    # Initialize context manager with configurable path
    context_path = config.get("context_storage", {}).get("file_path")
    context_manager = ContextManager(context_path)

    # Setup Tool Registry and Register Tools Dynamically
    registry = ToolRegistry(config.get("registry_config", {}))
    for tool_dict in config.get("tools", []):
        try:
            execute_path = tool_dict.get("execute_func", "")
            module_path, func_name = execute_path.rsplit(".", 1)
            mod = __import__(module_path, fromlist=[func_name])
            execute_func = getattr(mod, func_name)

            tool = Tool(
                name=tool_dict["name"],
                description=tool_dict["description"],
                execute_func=execute_func,
                schema=tool_dict.get("schema", {})
            )
            registry.register_tool(tool)
            reg_success_msg = log_messages.get("tool_registered", "Registered tool: {tool_name}")
            logger.info(reg_success_msg.format(tool_name=tool.name))
        except Exception as e:
            reg_error_msg = log_messages.get("tool_import_failed", "Could not import {tool_name}: {error}")
            logger.warning(reg_error_msg.format(tool_name=tool_dict.get('name'), error=e))

    try:
        # Get conversation context with configurable limit and format
        context_limit = config.get("context_limit", 3)
        context_format_config = config.get("context_format", {})
        context_messages = context_manager.get_recent_messages(user_id, limit=context_limit, format_config=context_format_config)

        # Set runtime context for this execution session
        context_manager.set_context("current_user_id", user_id)
        context_manager.set_context("current_query", user_input)
        context_manager.set_context("execution_timestamp", __import__('time').time())

        # Step 1: Ask LLM to select appropriate tool
        available_tools = registry.get_tools()
        tool_selection_prompt = _build_tool_selection_prompt(user_input, available_tools, context_messages, config)

        logger.info(" Asking LLM to select tool...")
        tool_decision_result = format_response(
            prompt=tool_selection_prompt,
            formatter="json",
            model_name=model_name,
            temperature=temperature,
            return_meta=True,
            system_prompt=system_prompt,
            messages=context_messages
        )

        # Step 2: Parse the tool decision with robust error handling
        if not tool_decision_result:
            logger.error("No response from LLM for tool selection")
            tool_decision = {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": "No response from LLM",
                "direct_response": "I'm having trouble processing your request. Please try again."
            }
        elif tool_decision_result.get("error"):
            logger.error(f"LLM API error: {tool_decision_result.get('error')}")
            tool_decision = {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": f"LLM API error: {tool_decision_result.get('error')}",
                "direct_response": "I'm experiencing technical difficulties. Please try again."
            }
        else:
            raw_decision = tool_decision_result.get("raw_response", "")
            parsed_decision = tool_decision_result.get("parsed_response", "")
            logger.info(f"LLM raw response: {raw_decision}")
            logger.info(f"🔧 Using model: {model_name}")

            # If parsed_response is already a dict, use it; otherwise extract from raw
            if isinstance(parsed_decision, dict) and "selected_tool" in parsed_decision:
                logger.info("Using pre-parsed JSON response")
                tool_decision = parsed_decision
            else:
                logger.info("Extracting tool decision from raw response")
                # Use raw response if parsed is not a proper dict
                decision_text = raw_decision if isinstance(parsed_decision, str) else str(parsed_decision)
                tool_decision = _extract_tool_decision(decision_text, config)

        selected_tool_name = tool_decision.get("selected_tool", "none")
        tool_parameters = tool_decision.get("parameters", {})
        reasoning = tool_decision.get("reasoning", "No reasoning provided")

        logger.info(f" LLM selected tool: {selected_tool_name} | Reasoning: {reasoning}")

        final_response = ""
        tool_result = None

        # Step 3: Execute the selected tool or provide direct response
        if selected_tool_name != "none":
            selected_tool = registry.get_tool(selected_tool_name)
            if selected_tool:
                logger.info(f"Executing tool: {selected_tool_name} with parameters: {tool_parameters}")
                try:
                    # Add registry and context manager to tool parameters for dynamic access
                    enhanced_params = tool_parameters.copy()
                    enhanced_params["registry"] = registry
                    enhanced_params["config"] = config
                    enhanced_params["user_id"] = user_id
                    enhanced_params["original_query"] = user_input
                    enhanced_params["context_manager"] = context_manager  # Add context manager access

                    # Allow AI to enhance parameters if configured
                    if config.get("enable_parameter_enhancement", False):
                        enhanced_params = _enhance_tool_parameters(enhanced_params, selected_tool, config)

                    tool_result = selected_tool.execute(enhanced_params)
                    logger.info(f" Tool execution successful: {tool_result}")

                    # Generate final response based on tool result
                    if config and "prompt_templates" in config and "final_response" in config["prompt_templates"]:
                        final_prompt = config["prompt_templates"]["final_response"].format(
                            user_input=user_input,
                            selected_tool_name=selected_tool_name,
                            tool_result=tool_result
                        )
                    else:
                        final_prompt = f"""Based on the tool execution result, provide a helpful response to the user.

User's original question: {user_input}
Tool used: {selected_tool_name}
Tool result: {tool_result}

Please provide a natural, helpful response to the user based on this information."""

                    final_result = format_response(
                        prompt=final_prompt,
                        formatter="answer",
                        model_name=model_name,
                        temperature=temperature,
                        return_meta=True,
                        system_prompt=system_prompt
                    )

                    final_response = final_result.get("parsed_response", str(tool_result))

                except Exception as e:
                    logger.error(f" Tool execution failed: {e}")
                    error_template = config.get("error_messages", {}).get("tool_execution_failed",
                        "I tried to use the {tool_name} tool, but encountered an error: {error}")
                    final_response = error_template.format(tool_name=selected_tool_name, error=str(e))
            else:
                logger.warning(f" Selected tool '{selected_tool_name}' not found in registry")
                not_found_template = config.get("error_messages", {}).get("tool_not_found",
                    "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}")
                fallback_response = tool_decision.get('direct_response', 'I apologize, but I cannot complete this request.')
                final_response = not_found_template.format(tool_name=selected_tool_name, fallback_response=fallback_response)
        else:
            # No tool needed, use direct response
            default_direct = config.get("default_responses", {}).get("no_tool_needed",
                "I can help you with that, but I'm not sure how to respond.")
            final_response = tool_decision.get("direct_response", default_direct)
            logger.info(" Providing direct response (no tool needed)")

        # Step 4: Save interaction to context
        context_manager.add_interaction(user_id, user_input, final_response)

        return {
            "input": user_input,
            "selected_tool": selected_tool_name,
            "tool_parameters": tool_parameters,
            "tool_result": tool_result,
            "reasoning": reasoning,
            "final_response": final_response,
            "used_model": model_name,
            "user_id": user_id
        }

    except Exception as e:
        logger.error(f" Agent execution failed: {e}")
        general_error_template = config.get("error_messages", {}).get("general_error",
            "I apologize, but I encountered an error while processing your request: {error}")
        error_response = general_error_template.format(error=str(e))

        # Still save the interaction even if there was an error
        try:
            context_manager.add_interaction(user_id, user_input, error_response)
        except:
            pass  # Don't let context saving errors break the main flow

        return {
            "input": user_input,
            "selected_tool": "none",
            "tool_parameters": {},
            "tool_result": None,
            "reasoning": "Error occurred during processing",
            "final_response": error_response,
            "used_model": model_name,
            "user_id": user_id,
            "error": str(e)
        }


