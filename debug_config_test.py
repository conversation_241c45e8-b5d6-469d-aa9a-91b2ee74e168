#!/usr/bin/env python3
"""
Debug script to test the configuration loading fix.
This helps identify exactly what's happening with config loading.
"""

import tempfile
import json
import os
import sys
from pathlib import Path

def test_config_loading():
    """Test configuration loading with detailed debugging."""
    print("🔍 Debugging Configuration Loading")
    print("=" * 50)
    
    # Add current directory to Python path for testing
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    try:
        from utils.toolkit import ConfigUtils
        print("✅ Successfully imported ConfigUtils")
    except ImportError as e:
        print(f"❌ Failed to import ConfigUtils: {e}")
        return False
    
    # Test 1: Default config loading
    print("\n📋 Test 1: Default Config Loading")
    try:
        default_config = ConfigUtils.load_config()
        print(f"   Config type: {type(default_config)}")
        print(f"   Config keys: {sorted(default_config.keys())}")
        print(f"   LLM model: {default_config.get('llm', {}).get('model', 'NOT_FOUND')}")
        assert isinstance(default_config, dict)
        assert "llm" in default_config
        assert "system_prompt" in default_config
        print("✅ Default config loading works")
    except Exception as e:
        print(f"❌ Default config loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 2: Custom config loading
    print("\n📋 Test 2: Custom Config Loading")
    test_config_data = {
        "test_setting": "debug_value",
        "llm": {"model": "debug-model", "temperature": 0.9},
        "custom_key": "custom_value",
        "nested": {"deep": {"value": "test"}}
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(test_config_data, f, indent=2)
        temp_path = f.name
    
    print(f"   Created temp config: {temp_path}")
    print(f"   File exists: {os.path.exists(temp_path)}")
    
    # Verify file content
    try:
        with open(temp_path, 'r') as f:
            file_content = json.load(f)
        print(f"   File content keys: {sorted(file_content.keys())}")
        print(f"   File test_setting: {file_content.get('test_setting')}")
    except Exception as e:
        print(f"   ❌ Could not read temp file: {e}")
        return False
    
    try:
        # Load the custom config
        custom_config = ConfigUtils.load_config(temp_path)
        print(f"   Loaded config type: {type(custom_config)}")
        print(f"   Loaded config keys: {sorted(custom_config.keys())}")
        
        # Check specific values
        test_setting = custom_config.get('test_setting')
        llm_model = custom_config.get('llm', {}).get('model')
        custom_key = custom_config.get('custom_key')
        
        print(f"   test_setting: {test_setting}")
        print(f"   llm.model: {llm_model}")
        print(f"   custom_key: {custom_key}")
        
        # Verify the values
        if test_setting == "debug_value":
            print("✅ test_setting loaded correctly")
        else:
            print(f"❌ test_setting wrong: expected 'debug_value', got '{test_setting}'")
            
        if llm_model == "debug-model":
            print("✅ llm.model loaded correctly")
        else:
            print(f"❌ llm.model wrong: expected 'debug-model', got '{llm_model}'")
            
        if custom_key == "custom_value":
            print("✅ custom_key loaded correctly")
        else:
            print(f"❌ custom_key wrong: expected 'custom_value', got '{custom_key}'")
        
        # Final assertions
        assert custom_config["test_setting"] == "debug_value"
        assert custom_config["llm"]["model"] == "debug-model"
        assert custom_config["custom_key"] == "custom_value"
        
        print("✅ Custom config loading works perfectly!")
        
    except Exception as e:
        print(f"❌ Custom config loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Cleanup
        if os.path.exists(temp_path):
            os.unlink(temp_path)
            print(f"   Cleaned up: {temp_path}")
    
    # Test 3: Non-existent file
    print("\n📋 Test 3: Non-existent File Handling")
    try:
        nonexistent_config = ConfigUtils.load_config("/nonexistent/path/config.json")
        print(f"   Config type: {type(nonexistent_config)}")
        print(f"   Has default keys: {'llm' in nonexistent_config and 'system_prompt' in nonexistent_config}")
        assert isinstance(nonexistent_config, dict)
        assert "llm" in nonexistent_config  # Should fall back to defaults
        print("✅ Non-existent file handling works (falls back to defaults)")
    except Exception as e:
        print(f"❌ Non-existent file handling failed: {e}")
        return False
    
    print("\n🎉 All configuration tests passed!")
    return True

if __name__ == "__main__":
    success = test_config_loading()
    if success:
        print("\n✅ Configuration system is working correctly!")
        print("The issue should be resolved now.")
    else:
        print("\n❌ Configuration system still has issues!")
        print("Further debugging needed.")
    
    sys.exit(0 if success else 1)
