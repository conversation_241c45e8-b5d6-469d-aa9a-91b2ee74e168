#!/usr/bin/env python3
"""
Runtime Context Usage Examples for AI Utility Orchestrator

This file demonstrates how to use the enhanced runtime context functionality
to create stateful, context-aware tools and applications.
"""

from ai_utility_orchestrator import agent_executor, <PERSON>lRegist<PERSON>, Tool, ConfigUtils
from ai_utility_orchestrator.utils import Context<PERSON>anager


def user_profile_tool(params):
    """Tool that manages user profile data using runtime context."""
    context_manager = params.get("context_manager")
    action = params.get("action", "get")
    
    if not context_manager:
        return "Context manager not available"
    
    if action == "set":
        name = params.get("name", "")
        age = params.get("age", 0)
        context_manager.set_context("user_profile", {"name": name, "age": age})
        return f"Profile set for {name}, age {age}"
    
    elif action == "get":
        profile = context_manager.get_context("user_profile", {})
        if profile:
            return f"User: {profile.get('name', 'Unknown')}, Age: {profile.get('age', 'Unknown')}"
        return "No profile found"


def session_counter_tool(params):
    """Tool that tracks session statistics using runtime context."""
    context_manager = params.get("context_manager")
    
    if not context_manager:
        return "Context manager not available"
    
    # Increment tool usage counter
    count = context_manager.get_context("tool_usage_count", 0) + 1
    context_manager.set_context("tool_usage_count", count)
    
    # Track unique tools used
    used_tools = context_manager.get_context("used_tools", set())
    current_tool = params.get("tool_name", "unknown")
    used_tools.add(current_tool)
    context_manager.set_context("used_tools", used_tools)
    
    return f"Tool usage count: {count}, Unique tools used: {len(used_tools)}"


def context_summary_tool(params):
    """Tool that provides a summary of all runtime context."""
    context_manager = params.get("context_manager")
    
    if not context_manager:
        return "Context manager not available"
    
    all_context = context_manager.get_all_context()
    
    summary = "Runtime Context Summary:\n"
    for key, value in all_context.items():
        if isinstance(value, (dict, list, set)):
            summary += f"- {key}: {type(value).__name__} with {len(value)} items\n"
        else:
            summary += f"- {key}: {value}\n"
    
    return summary


def example_basic_usage():
    """Example: Basic runtime context usage."""
    print("=== Basic Runtime Context Usage ===")
    
    # Create context manager
    cm = ContextManager()
    
    # Set some context data
    cm.set_context("user_name", "Alice")
    cm.set_context("session_id", "sess_123")
    cm.set_context("preferences", {"theme": "dark", "language": "en"})
    
    # Retrieve context data
    print(f"User: {cm.get_context('user_name')}")
    print(f"Session: {cm.get_context('session_id')}")
    print(f"Theme: {cm.get_context('preferences', {}).get('theme')}")
    
    # Check context existence
    print(f"Has user_name: {cm.has_context('user_name')}")
    print(f"Has nonexistent: {cm.has_context('nonexistent_key')}")
    
    # Update multiple values
    cm.update_context({
        "last_action": "login",
        "timestamp": "2024-01-01T10:00:00Z"
    })
    
    # Get all context
    print(f"All context keys: {list(cm.get_all_context().keys())}")
    
    # Clear context
    cm.clear_context()
    print(f"Context after clear: {len(cm.get_all_context())} items")


def example_tool_integration():
    """Example: Using runtime context with tools."""
    print("\n=== Tool Integration with Runtime Context ===")
    
    # Create configuration with context-aware tools
    config = {
        "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
        "tools": [
            {
                "name": "profile_tool",
                "description": "Manage user profile data",
                "execute_func": "examples.runtime_context_usage.user_profile_tool",
                "schema": {
                    "type": "object",
                    "properties": {
                        "action": {"type": "string", "enum": ["get", "set"]},
                        "name": {"type": "string"},
                        "age": {"type": "integer"}
                    }
                }
            },
            {
                "name": "counter_tool",
                "description": "Track session statistics",
                "execute_func": "examples.runtime_context_usage.session_counter_tool",
                "schema": {"type": "object", "properties": {}}
            },
            {
                "name": "summary_tool",
                "description": "Show runtime context summary",
                "execute_func": "examples.runtime_context_usage.context_summary_tool",
                "schema": {"type": "object", "properties": {}}
            }
        ]
    }
    
    # Simulate tool usage (in real usage, this would be through agent_executor)
    registry = ToolRegistry()
    cm = ContextManager()
    
    # Register tools
    for tool_config in config["tools"]:
        # In real usage, this would be done automatically by agent_executor
        print(f"Tool registered: {tool_config['name']}")
    
    # Simulate tool executions with context
    profile_params = {
        "context_manager": cm,
        "action": "set",
        "name": "Bob",
        "age": 25
    }
    print(f"Profile tool result: {user_profile_tool(profile_params)}")
    
    counter_params = {"context_manager": cm, "tool_name": "profile_tool"}
    print(f"Counter tool result: {session_counter_tool(counter_params)}")
    
    summary_params = {"context_manager": cm}
    print(f"Summary tool result:\n{context_summary_tool(summary_params)}")


def example_session_persistence():
    """Example: Session-level data persistence."""
    print("\n=== Session-Level Data Persistence ===")
    
    cm = ContextManager()
    
    # Simulate a multi-step workflow
    steps = [
        {"step": 1, "action": "user_login", "data": {"user_id": "user123"}},
        {"step": 2, "action": "load_preferences", "data": {"theme": "dark", "lang": "en"}},
        {"step": 3, "action": "process_request", "data": {"request_id": "req456"}},
        {"step": 4, "action": "generate_response", "data": {"response_type": "json"}},
    ]
    
    # Process each step, accumulating context
    for step_info in steps:
        step_num = step_info["step"]
        action = step_info["action"]
        data = step_info["data"]
        
        # Store step-specific data
        cm.set_context(f"step_{step_num}_action", action)
        cm.update_context(data)
        
        # Track workflow progress
        completed_steps = cm.get_context("completed_steps", [])
        completed_steps.append(step_num)
        cm.set_context("completed_steps", completed_steps)
        
        print(f"Step {step_num}: {action} - Context size: {len(cm.get_all_context())}")
    
    # Final workflow summary
    print(f"Workflow completed. Steps: {cm.get_context('completed_steps')}")
    print(f"Final user: {cm.get_context('user_id')}")
    print(f"Final theme: {cm.get_context('theme')}")


if __name__ == "__main__":
    example_basic_usage()
    example_tool_integration()
    example_session_persistence()
    
    print("\n=== Runtime Context Enhancement Complete! ===")
    print("Your AI Utility Orchestrator now supports:")
    print("✅ Session-level state management")
    print("✅ Tool-to-tool data sharing")
    print("✅ Workflow context persistence")
    print("✅ Dynamic runtime configuration")
    print("✅ Enhanced debugging capabilities")
