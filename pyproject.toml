[project]
name = "ai_utility_orchestrator"
version = "0.1.0"
description = "A universal, AI-powered utility orchestration package with dynamic LLM-driven routing."
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
  { name = "<PERSON><PERSON><PERSON>" }
]

dependencies = [
  "openai",
  "python-dotenv",
  "PyYAML",
  "importlib-resources>=1.3.0; python_version<'3.9'"
]

[project.optional-dependencies]
dev = [
  "pytest>=7.0.0",
  "pytest-cov>=4.0.0",
  "pytest-mock>=3.10.0",
  "black>=23.0.0",
  "mypy>=1.0.0"
]
test = [
  "pytest>=7.0.0",
  "pytest-cov>=4.0.0",
  "pytest-mock>=3.10.0"
]

[tool.setuptools]
packages = ["ai_utility_orchestrator", "ai_utility_orchestrator.core", "ai_utility_orchestrator.utils"]

[tool.setuptools.package-dir]
"ai_utility_orchestrator" = "."
"ai_utility_orchestrator.core" = "core"
"ai_utility_orchestrator.utils" = "utils"

[tool.setuptools.package-data]
"ai_utility_orchestrator" = ["config/*.json"]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings",
]
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "unit: marks tests as unit tests",
]
