#!/usr/bin/env python3
"""
Quick test to verify the ConfigUtils.load_config fix works correctly.
"""

import tempfile
import json
import os

def test_config_loading():
    """Test the fixed configuration loading."""
    print("Testing ConfigUtils.load_config fix...")
    
    try:
        # This would normally fail if the package isn't installed
        # but we can test the logic
        import sys
        sys.path.insert(0, '.')  # Add current directory to path
        
        from utils.toolkit import ConfigUtils
        
        print("✅ Successfully imported ConfigUtils")
        
        # Test 1: Default config loading
        print("🔄 Testing default config loading...")
        config = ConfigUtils.load_config()
        print(f"   Default config type: {type(config)}")
        print(f"   Default config keys: {list(config.keys())}")
        assert isinstance(config, dict)
        assert "llm" in config
        print("✅ Default config loading works")
        
        # Test 2: Custom config loading
        print("🔄 Testing custom config loading...")
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_config = {
                "test_setting": "validation_value",
                "llm": {"model": "test-model", "temperature": 0.8},
                "custom_key": "custom_value"
            }
            json.dump(test_config, f)
            temp_path = f.name
        
        print(f"   Created temp config file: {temp_path}")
        
        try:
            # Load the custom config
            custom_config = ConfigUtils.load_config(temp_path)
            print(f"   Custom config type: {type(custom_config)}")
            print(f"   Custom config keys: {list(custom_config.keys())}")
            
            # Check if our custom settings are there
            if "test_setting" in custom_config:
                print(f"   ✅ Custom setting found: {custom_config['test_setting']}")
                assert custom_config["test_setting"] == "validation_value"
                assert custom_config["llm"]["model"] == "test-model"
                print("✅ Custom config loading works perfectly!")
            else:
                print("   ⚠️  Custom settings not found - this indicates the old bug")
                print("   This means the fix didn't work as expected")
                return False
                
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
                print(f"   Cleaned up temp file: {temp_path}")
        
        print("🎉 All configuration tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   This is expected if the package isn't installed")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_loading()
    if success:
        print("\n✅ Configuration fix verified!")
        print("The ConfigUtils.load_config method should now work correctly.")
    else:
        print("\n❌ Configuration fix verification failed!")
        print("There may still be issues with the config loading logic.")
