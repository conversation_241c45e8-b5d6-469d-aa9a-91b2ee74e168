#!/usr/bin/env python3
"""
Test that exactly mimics the failing test to debug the issue.
"""

import tempfile
import json
import os
import sys
from pathlib import Path

def test_exact_failure():
    """Replicate the exact test that's failing."""
    print("🔍 Testing Exact Failure Scenario")
    print("=" * 40)
    
    # Add current directory to path
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    try:
        # This is exactly what the failing test does
        from ai_utility_orchestrator import ConfigUtils
        print("✅ Import successful")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        print("This means the package isn't properly installed")
        return False
    
    try:
        # Test default config loading (this part usually works)
        print("\n📋 Testing default config loading...")
        config = ConfigUtils.load_config()
        assert "llm" in config
        assert "system_prompt" in config
        assert "tools" in config
        print("✅ Default config loading works")
        
        # Test config with custom file (this is where it fails)
        print("\n📋 Testing custom config loading...")
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_config = {"test_setting": "test_value", "llm": {"model": "test-model"}}
            json.dump(test_config, f)
            temp_path = f.name
        
        print(f"   Created temp file: {temp_path}")
        print(f"   File exists: {os.path.exists(temp_path)}")
        
        # Read back to verify
        with open(temp_path, 'r') as f:
            verify_content = json.load(f)
        print(f"   File content: {verify_content}")
        
        try:
            # This is the exact line that fails
            loaded_config = ConfigUtils.load_config(temp_path)
            print(f"   Loaded config type: {type(loaded_config)}")
            print(f"   Loaded config keys: {list(loaded_config.keys())}")
            
            # Check if test_setting exists
            if "test_setting" in loaded_config:
                print(f"   ✅ test_setting found: {loaded_config['test_setting']}")
                assert loaded_config["test_setting"] == "test_value"
                print("   ✅ test_setting value correct")
            else:
                print(f"   ❌ test_setting NOT found in loaded config")
                print(f"   Available keys: {list(loaded_config.keys())}")
                raise AssertionError("test_setting not found in loaded config")
            
            # Check if llm.model exists
            if "llm" in loaded_config and "model" in loaded_config["llm"]:
                print(f"   ✅ llm.model found: {loaded_config['llm']['model']}")
                assert loaded_config["llm"]["model"] == "test-model"
                print("   ✅ llm.model value correct")
            else:
                print(f"   ❌ llm.model NOT found")
                print(f"   llm section: {loaded_config.get('llm', 'NOT_FOUND')}")
                raise AssertionError("llm.model not found in loaded config")
                
        finally:
            os.unlink(temp_path)
            print(f"   Cleaned up: {temp_path}")
        
        print("✅ Custom config loading test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_exact_failure()
    if success:
        print("\n🎉 The configuration test should now work!")
    else:
        print("\n💥 The test is still failing - need more investigation")
    
    sys.exit(0 if success else 1)
