#!/usr/bin/env python3
"""
AI Utility Orchestrator - Real World Scenario Test

This script tests the AI Utility Orchestrator package with actual OpenAI API calls
to validate the complete pipeline works in real-world conditions.

REQUIREMENTS:
- ai_utility_orchestrator package installed
- Valid OpenAI API key set in environment or .env file
- Internet connection

Usage:
    # Set your API key
    export OPENAI_API_KEY="your-actual-api-key-here"
    
    # Run the test
    python test_real_world_scenario.py

This test will make actual API calls and may incur small costs (~$0.01-0.05).
"""

import sys
import os
import tempfile
import json
import time
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*70}")
    print(f" {title}")
    print(f"{'='*70}")

def print_test(test_name, status="RUNNING"):
    """Print test status."""
    if status == "RUNNING":
        print(f"🔄 {test_name}...", end=" ", flush=True)
    elif status == "PASS":
        print("✅ PASSED")
    elif status == "FAIL":
        print("❌ FAILED")
    elif status == "SKIP":
        print("⏭️  SKIPPED")

def check_api_key():
    """Check if OpenAI API key is available."""
    print_test("Checking OpenAI API key", "RUNNING")
    
    # Check environment variable
    api_key = os.getenv('OPENAI_API_KEY')
    
    # Check .env file if not in environment
    if not api_key:
        try:
            from dotenv import load_dotenv
            load_dotenv()
            api_key = os.getenv('OPENAI_API_KEY')
        except ImportError:
            pass
    
    if not api_key:
        print_test("Checking OpenAI API key", "FAIL")
        print("   Error: No OpenAI API key found!")
        print("   Please set OPENAI_API_KEY environment variable or add it to .env file")
        print("   Example: export OPENAI_API_KEY='your-api-key-here'")
        return False
    
    if not api_key.startswith('sk-'):
        print_test("Checking OpenAI API key", "FAIL")
        print("   Error: API key format appears invalid (should start with 'sk-')")
        return False
    
    print_test("Checking OpenAI API key", "PASS")
    print(f"   Using API key: {api_key[:10]}...{api_key[-4:]}")
    return True

def test_direct_response():
    """Test 1: Direct response without tools."""
    print_test("Testing direct response (no tools)", "RUNNING")
    
    try:
        from ai_utility_orchestrator import agent_executor
        
        # Simple configuration without tools
        config = {
            "llm": {
                "model": "gpt-4o-mini",
                "temperature": 0.3,
                "max_tokens": 100
            },
            "system_prompt": "You are a helpful assistant. Give brief, direct answers.",
            "default_user_id": "real_world_test",
            "context_limit": 2,
            "tools": []  # No tools - should give direct response
        }
        
        # Test with a simple question
        result = agent_executor("What is the capital of France?", config=config)
        
        # Validate response structure
        assert isinstance(result, dict)
        assert "final_response" in result
        assert "selected_tool" in result
        assert result["selected_tool"] == "none"
        assert "Paris" in result["final_response"]
        
        print_test("Testing direct response (no tools)", "PASS")
        print(f"   Response: {result['final_response'][:100]}...")
        return True
        
    except Exception as e:
        print_test("Testing direct response (no tools)", "FAIL")
        print(f"   Error: {e}")
        return False

def test_with_custom_tools():
    """Test 2: Agent executor with custom tools."""
    print_test("Testing with custom tools", "RUNNING")
    
    try:
        from ai_utility_orchestrator import agent_executor
        
        # Configuration with custom tools
        config = {
            "llm": {
                "model": "gpt-4o-mini",
                "temperature": 0.3,
                "max_tokens": 200
            },
            "system_prompt": "You are an AI assistant that can use tools to help users. Always try to use the most appropriate tool for the task.",
            "default_user_id": "real_world_test",
            "context_limit": 3,
            "tools": [
                {
                    "name": "calculator",
                    "description": "Performs mathematical calculations and arithmetic operations",
                    "execute_func": "test_real_world_scenario.real_calculator",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "expression": {
                                "type": "string", 
                                "description": "Mathematical expression to calculate (e.g., '2+2', '10*5', '100/4')"
                            }
                        },
                        "required": ["expression"]
                    }
                },
                {
                    "name": "text_analyzer",
                    "description": "Analyzes text and provides statistics like word count, character count, etc.",
                    "execute_func": "test_real_world_scenario.text_analyzer",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Text to analyze"
                            },
                            "analysis_type": {
                                "type": "string",
                                "description": "Type of analysis: 'basic' for word/char count, 'detailed' for more stats",
                                "enum": ["basic", "detailed"]
                            }
                        },
                        "required": ["text"]
                    }
                }
            ]
        }
        
        # Test mathematical calculation
        print("\n   Testing calculator tool...")
        result = agent_executor("What is 15 multiplied by 8?", config=config)
        
        assert isinstance(result, dict)
        assert result["selected_tool"] == "calculator"
        assert "120" in result["tool_result"]
        
        print(f"   Calculator result: {result['tool_result']}")
        print(f"   Final response: {result['final_response'][:100]}...")
        
        # Test text analysis
        print("\n   Testing text analyzer tool...")
        result = agent_executor("Analyze this text: 'Hello world, this is a test message for analysis.'", config=config)
        
        assert isinstance(result, dict)
        assert result["selected_tool"] == "text_analyzer"
        assert "words" in result["tool_result"].lower()
        
        print(f"   Text analysis result: {result['tool_result']}")
        print(f"   Final response: {result['final_response'][:100]}...")
        
        print_test("Testing with custom tools", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing with custom tools", "FAIL")
        print(f"   Error: {e}")
        return False

def test_conversation_context():
    """Test 3: Multi-turn conversation with context."""
    print_test("Testing conversation context", "RUNNING")
    
    try:
        from ai_utility_orchestrator import agent_executor
        
        # Create temporary directory for context storage
        with tempfile.TemporaryDirectory() as temp_dir:
            config = {
                "llm": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.3,
                    "max_tokens": 150
                },
                "system_prompt": "You are a helpful assistant. Remember the conversation context.",
                "default_user_id": "context_test_user",
                "context_limit": 3,
                "context_storage": {
                    "backend": "file",
                    "file_path": str(Path(temp_dir) / "context_test.json")
                },
                "tools": []
            }
            
            # First interaction
            print("\n   First interaction...")
            result1 = agent_executor("My name is Alice and I like programming.", config=config, user_id="context_test_user")
            print(f"   Response 1: {result1['final_response'][:80]}...")
            
            # Second interaction - should remember the name
            print("\n   Second interaction (testing context)...")
            result2 = agent_executor("What's my name and what do I like?", config=config, user_id="context_test_user")
            print(f"   Response 2: {result2['final_response'][:80]}...")
            
            # Validate context was used
            assert "Alice" in result2['final_response'] or "alice" in result2['final_response'].lower()
            assert "programming" in result2['final_response'].lower() or "program" in result2['final_response'].lower()
            
        print_test("Testing conversation context", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing conversation context", "FAIL")
        print(f"   Error: {e}")
        return False

def test_error_handling():
    """Test 4: Error handling scenarios."""
    print_test("Testing error handling", "RUNNING")
    
    try:
        from ai_utility_orchestrator import agent_executor
        
        # Test with invalid tool reference
        config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.3},
            "system_prompt": "You are a helpful assistant.",
            "tools": [
                {
                    "name": "broken_tool",
                    "description": "A tool that will fail to import",
                    "execute_func": "nonexistent.module.function",
                    "schema": {"type": "object", "properties": {}}
                }
            ]
        }
        
        # This should handle the error gracefully
        result = agent_executor("Use the broken tool", config=config)
        
        # Should still return a valid response structure
        assert isinstance(result, dict)
        assert "final_response" in result
        
        print_test("Testing error handling", "PASS")
        print(f"   Handled error gracefully: {result['final_response'][:60]}...")
        return True
        
    except Exception as e:
        print_test("Testing error handling", "FAIL")
        print(f"   Error: {e}")
        return False

def test_configuration_loading():
    """Test 5: Configuration loading from file."""
    print_test("Testing configuration loading", "RUNNING")
    
    try:
        from ai_utility_orchestrator import ConfigUtils, agent_executor
        
        # Create a custom config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            custom_config = {
                "llm": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.2,
                    "max_tokens": 100
                },
                "system_prompt": "You are a test assistant for configuration validation.",
                "default_user_id": "config_test_user",
                "context_limit": 1,
                "tools": [],
                "custom_setting": "test_value_12345"
            }
            json.dump(custom_config, f)
            config_path = f.name
        
        try:
            # Load config from file
            loaded_config = ConfigUtils.load_config(config_path)
            assert loaded_config["custom_setting"] == "test_value_12345"
            assert loaded_config["llm"]["temperature"] == 0.2
            
            # Test using the loaded config
            result = agent_executor("Say hello", config=loaded_config)
            assert isinstance(result, dict)
            assert "final_response" in result
            
        finally:
            os.unlink(config_path)
        
        print_test("Testing configuration loading", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing configuration loading", "FAIL")
        print(f"   Error: {e}")
        return False

# Tool functions for testing
def real_calculator(params):
    """Real calculator tool for testing."""
    expression = params.get("expression", "")
    try:
        # Simple evaluation for basic math
        # Note: In production, use a safer math parser
        result = eval(expression.replace("x", "*").replace("×", "*"))
        return f"{expression} = {result}"
    except Exception as e:
        return f"Error calculating {expression}: {str(e)}"

def text_analyzer(params):
    """Text analyzer tool for testing."""
    text = params.get("text", "")
    analysis_type = params.get("analysis_type", "basic")
    
    if not text:
        return "No text provided for analysis"
    
    word_count = len(text.split())
    char_count = len(text)
    char_count_no_spaces = len(text.replace(" ", ""))
    
    if analysis_type == "basic":
        return f"Text analysis: {word_count} words, {char_count} characters"
    else:
        sentences = text.count('.') + text.count('!') + text.count('?')
        return f"Detailed analysis: {word_count} words, {char_count} characters ({char_count_no_spaces} without spaces), ~{sentences} sentences"

def main():
    """Run all real-world scenario tests."""
    print_header("AI Utility Orchestrator - Real World Scenario Test")
    print("⚠️  WARNING: This test makes actual OpenAI API calls and may incur small costs!")
    print("💰 Estimated cost: $0.01 - $0.05 USD")
    print("🌐 Requires: Valid OpenAI API key and internet connection")
    
    # Check if user wants to continue
    try:
        response = input("\nDo you want to continue? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("Test cancelled by user.")
            return 0
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        return 0
    
    # Check API key first
    if not check_api_key():
        return 1
    
    print("\n🚀 Starting real-world tests...")
    
    # Run all tests
    tests = [
        test_direct_response,
        test_with_custom_tools,
        test_conversation_context,
        test_error_handling,
        test_configuration_loading
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            time.sleep(1)  # Small delay between API calls
        except KeyboardInterrupt:
            print("\n\nTest interrupted by user.")
            break
    
    # Print summary
    print_header("Real World Test Summary")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 SUCCESS: AI Utility Orchestrator works perfectly in real-world scenarios!")
        print("\n✅ Your package is ready for production use!")
        print("✅ All API integrations are working correctly!")
        print("✅ Tool system is functioning properly!")
        print("✅ Context management is working!")
        print("✅ Error handling is robust!")
        
        print("\n📖 Next steps:")
        print("   1. Your package is fully validated and ready to use")
        print("   2. Share this test with users to validate their installations")
        print("   3. Users can run this test to ensure everything works in their environment")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Verify your OpenAI API key is valid and has credits")
        print("   2. Check your internet connection")
        print("   3. Ensure the package is properly installed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
