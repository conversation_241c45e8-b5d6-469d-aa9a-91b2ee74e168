"""
Comprehensive test suite for AI Utility Orchestrator package.

This single test file validates the entire package functionality including:
- Core agent execution
- Tool registration and execution
- Configuration loading
- Context management
- Response formatting
- Error handling

Run with: pytest tests/test_ai_utility_orchestrator.py -v
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch

# Import the main package components
from ai_utility_orchestrator import agent_executor, ToolRegistry, Tool, ConfigUtils
from ai_utility_orchestrator.utils.context_manager import ContextManager
from ai_utility_orchestrator.utils.response_formatter import format_response


class TestAIUtilityOrchestrator:
    """Comprehensive test suite for the entire AI Utility Orchestrator package."""

    @pytest.fixture(autouse=True)
    def setup_test_environment(self):
        """Set up test environment with mock API key."""
        original_env = os.environ.copy()
        os.environ['OPENAI_API_KEY'] = 'test-api-key-for-testing'
        yield
        os.environ.clear()
        os.environ.update(original_env)

    @pytest.fixture
    def sample_config(self):
        """Create a sample configuration for testing."""
        return {
            "llm": {
                "model": "gpt-4o-mini",
                "temperature": 0.5,
                "max_tokens": 800
            },
            "system_prompt": "You are a helpful AI assistant for testing.",
            "default_user_id": "test_user",
            "context_limit": 3,
            "enable_parameter_enhancement": False,
            "enable_ai_response_generation": False,
            "context_storage": {
                "backend": "file",
                "file_path": None
            },
            "context_format": {
                "user_role": "user",
                "assistant_role": "assistant",
                "include_metadata": False
            },
            "tools": [
                {
                    "name": "calculator",
                    "description": "Performs basic mathematical calculations",
                    "execute_func": "tests.test_ai_utility_orchestrator.mock_calculator",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "expression": {"type": "string", "description": "Mathematical expression"}
                        },
                        "required": ["expression"]
                    }
                },
                {
                    "name": "weather",
                    "description": "Gets weather information for a location",
                    "execute_func": "tests.test_ai_utility_orchestrator.mock_weather",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "location": {"type": "string", "description": "City name"}
                        },
                        "required": ["location"]
                    }
                }
            ],
            "error_messages": {
                "tool_execution_failed": "Tool {tool_name} failed: {error}",
                "tool_not_found": "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}",
                "general_error": "Error: {error}"
            },
            "default_responses": {
                "no_tool_needed": "I can help you with that directly."
            }
        }

    def test_package_imports(self):
        """Test that all main package components can be imported successfully."""
        # Test main imports
        assert agent_executor is not None
        assert ToolRegistry is not None
        assert Tool is not None
        assert ConfigUtils is not None
        
        # Test utility imports
        assert ContextManager is not None
        assert format_response is not None
        
        print("All package imports successful")

    def test_tool_creation_and_registry(self):
        """Test tool creation and registry functionality."""
        # Create a simple tool
        def simple_tool(params):
            return f"Processed: {params.get('input', 'no input')}"
        
        tool = Tool(
            name="simple_tool",
            description="A simple test tool",
            execute_func=simple_tool,
            schema={"type": "object", "properties": {"input": {"type": "string"}}}
        )
        
        # Test tool properties
        assert tool.name == "simple_tool"
        assert tool.description == "A simple test tool"
        assert callable(tool.execute)
        
        # Test registry
        registry = ToolRegistry()
        registry.register_tool(tool)
        
        # Verify tool is registered
        tools = registry.get_tools()
        assert len(tools) == 1
        assert tools[0].name == "simple_tool"
        
        # Test tool execution
        result = tool.execute({"input": "test data"})
        assert result == "Processed: test data"
        
        print("Tool creation and registry tests passed")

    def test_configuration_loading(self):
        """Test configuration loading functionality."""
        # Test default config loading
        config = ConfigUtils.load_config()
        assert "llm" in config
        assert "system_prompt" in config
        assert "tools" in config
        
        # Test config with custom file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_config = {"test_setting": "test_value", "llm": {"model": "test-model"}}
            json.dump(test_config, f)
            temp_path = f.name
        
        try:
            loaded_config = ConfigUtils.load_config(temp_path)
            assert loaded_config["test_setting"] == "test_value"
            assert loaded_config["llm"]["model"] == "test-model"
        finally:
            os.unlink(temp_path)
        
        print("Configuration loading tests passed")

    def test_context_management(self):
        """Test context management functionality."""
        with tempfile.TemporaryDirectory() as temp_dir:
            history_path = Path(temp_dir) / "test_history.json"
            cm = ContextManager(str(history_path))
            
            # Test adding interactions
            cm.add_interaction("user1", "Hello", "Hi there!")
            cm.add_interaction("user1", "How are you?", "I'm doing well!")
            
            # Test retrieving history
            history = cm.get_history("user1")
            assert len(history) == 2
            assert history[0]["user"] == "Hello"
            assert history[0]["bot"] == "Hi there!"
            
            # Test recent messages formatting
            messages = cm.get_recent_messages("user1", limit=2)
            assert len(messages) == 4  # 2 interactions = 4 messages (user + bot each)
            assert messages[0]["role"] == "user"
            assert messages[1]["role"] == "assistant"
            
            print("Context management tests passed")

    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_response_formatting(self, mock_client):
        """Test response formatting functionality."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response from AI"
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test basic response formatting
        result = format_response("Test prompt", formatter="answer")
        assert result == "Test response from AI"
        
        # Test JSON response formatting
        mock_response.choices[0].message.content = '{"result": "success", "data": 123}'
        result = format_response("Test prompt", formatter="json")
        assert isinstance(result, dict)
        assert result["result"] == "success"
        
        # Test with metadata
        result = format_response("Test prompt", return_meta=True)
        assert isinstance(result, dict)
        assert "raw_response" in result
        assert "parsed_response" in result
        assert "used_model" in result
        
        print("Response formatting tests passed")

    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_agent_executor_direct_response(self, mock_client, sample_config):
        """Test agent executor with direct response (no tool needed)."""
        # Mock LLM response for direct answer
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '{"selected_tool": "none", "parameters": {}, "reasoning": "Direct answer", "direct_response": "The capital of France is Paris."}'
        mock_client.chat.completions.create.return_value = mock_response
        
        result = agent_executor("What is the capital of France?", config=sample_config)
        
        assert result["selected_tool"] == "none"
        assert "Paris" in result["final_response"]
        assert result["reasoning"] == "Direct answer"
        
        print("Agent executor direct response test passed")

    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_agent_executor_with_tool(self, mock_client, sample_config):
        """Test agent executor with tool execution."""
        # Mock LLM response for tool selection
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '{"selected_tool": "calculator", "parameters": {"expression": "2 + 2"}, "reasoning": "Using calculator for math", "direct_response": ""}'
        mock_client.chat.completions.create.return_value = mock_response
        
        result = agent_executor("What is 2 + 2?", config=sample_config)
        
        assert result["selected_tool"] == "calculator"
        assert result["tool_parameters"]["expression"] == "2 + 2"
        assert "4" in result["tool_result"]  # Mock calculator returns "2 + 2 = 4"
        
        print("Agent executor with tool test passed")

    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_error_handling(self, mock_client, sample_config):
        """Test error handling in various scenarios."""
        # Test API error
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        result = agent_executor("Test query", config=sample_config)
        assert "technical difficulties" in result["final_response"].lower()
        
        # Test tool not found
        mock_client.chat.completions.create.side_effect = None
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '{"selected_tool": "nonexistent_tool", "parameters": {}, "reasoning": "Test", "direct_response": "Fallback"}'
        mock_client.chat.completions.create.return_value = mock_response
        
        result = agent_executor("Test query", config=sample_config)
        assert result["selected_tool"] == "nonexistent_tool"
        assert "not available" in result["final_response"]
        
        print("Error handling tests passed")

    def test_end_to_end_workflow(self, sample_config):
        """Test complete end-to-end workflow without external dependencies."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Set up context storage in temp directory
            sample_config["context_storage"]["file_path"] = str(Path(temp_dir) / "test_history.json")
            
            # Mock the LLM calls to avoid external dependencies
            with patch('ai_utility_orchestrator.utils.response_formatter.client') as mock_client:
                mock_response = Mock()
                mock_response.choices = [Mock()]
                mock_response.choices[0].message.content = '{"selected_tool": "weather", "parameters": {"location": "London"}, "reasoning": "Getting weather info", "direct_response": ""}'
                mock_client.chat.completions.create.return_value = mock_response
                
                # Execute the agent
                result = agent_executor("What's the weather in London?", config=sample_config, user_id="test_user")
                
                # Verify the complete workflow
                assert result["input"] == "What's the weather in London?"
                assert result["selected_tool"] == "weather"
                assert result["tool_parameters"]["location"] == "London"
                assert "London" in result["tool_result"]
                assert result["user_id"] == "test_user"
                
                # Verify context was saved
                cm = ContextManager(sample_config["context_storage"]["file_path"])
                history = cm.get_history("test_user")
                assert len(history) == 1
                assert "London" in history[0]["user"]
        
        print("End-to-end workflow test passed")

    def test_package_version_and_metadata(self):
        """Test package version and metadata."""
        import ai_utility_orchestrator
        assert hasattr(ai_utility_orchestrator, '__version__')
        assert ai_utility_orchestrator.__version__ == "0.1.0"
        
        print("Package metadata test passed")


# Mock tool functions for testing
def mock_calculator(params):
    """Mock calculator tool for testing."""
    expression = params.get("expression", "")
    if "2 + 2" in expression:
        return "2 + 2 = 4"
    elif "+" in expression:
        return f"{expression} = calculated result"
    else:
        return f"Calculated: {expression}"


def mock_weather(params):
    """Mock weather tool for testing."""
    location = params.get("location", "Unknown")
    return f"Weather in {location}: Sunny, 22°C"


if __name__ == "__main__":
    print("Running AI Utility Orchestrator comprehensive tests...")
    pytest.main([__file__, "-v"])
