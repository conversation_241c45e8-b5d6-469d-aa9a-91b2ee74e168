# ai_utility_orchestrator/utils/response_formatter.py

import os
import json
import yaml
import ast
import re
from dotenv import load_dotenv
from typing import Any, Union, Optional, Dict

# Load .env variables
load_dotenv()

try:
    from openai import OpenAI
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        client = OpenAI(api_key=api_key)
    else:
        # Don't initialize client if no API key is provided
        # This allows tests to run without requiring an API key
        client = None
except ImportError:
    print("Warning: OpenAI package not installed. Some features may not work.")
    client = None
except Exception as e:
    print(f"Warning: Could not initialize OpenAI client: {e}")
    client = None


def _parse_response(raw_text: str, formatter: str) -> Union[dict, list, str, None]:
    """Helper function to parse raw LLM response based on specified formatter."""
    if not raw_text or not raw_text.strip():
        return None

    try:
        if formatter == "json":
            # Try direct JSON parsing first
            cleaned_text = raw_text.strip()
            if cleaned_text.startswith('{') and cleaned_text.endswith('}'):
                return json.loads(cleaned_text)

            # Try to extract JSO<PERSON> from response with code blocks
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', cleaned_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))

            # Try to find any JSON-like structure
            json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())

            # If no JSON found, return the raw text
            print(f"Warning: Could not extract JSON from response, returning raw text")
            return cleaned_text

        elif formatter == "yaml":
            return yaml.safe_load(raw_text)
        elif formatter == "python":
            return ast.literal_eval(raw_text)
        else:
            return raw_text  # For 'answer' or unsupported formatter

    except json.JSONDecodeError as e:
        print(f"JSON parsing failed: {e}")
        print(f"Raw text was: {raw_text[:200]}...")
        # Return raw text instead of None to preserve the response
        return raw_text.strip()
    except Exception as e:
        print(f"Failed to parse LLM output using formatter '{formatter}': {e}")
        # Return raw text instead of None to preserve the response
        return raw_text.strip() if raw_text else None


def format_response(
    prompt: str,
    formatter: str = "answer",
    model_name: str = "gpt-4o-mini",
    temperature: float = 0.7,
    return_meta: bool = False,
    system_prompt: str = None,
    messages: list = None
) -> Union[Dict[str, Any], str, None]:
    """
    Calls the LLM and formats the output.

    Args:
        prompt (str): The user query or prompt.
        formatter (str): Format type: 'json', 'yaml', 'python', or 'answer'.
        model_name (str): OpenAI model name.
        temperature (float): Temperature for creativity.
        return_meta (bool): Whether to return metadata like raw response and model used.
        system_prompt (str): System prompt to guide the LLM.
        messages (list): Previous conversation messages for context.

    Returns:
        If return_meta is True:
            dict: {
                "raw_response": str,
                "parsed_response": Any,
                "used_model": str,
                "error": Optional[str]
            }
        Else:
            Any (parsed response only)
    """
    if not client:
        error_msg = "OpenAI client not initialized. Check your API key."
        if return_meta:
            return {
                "raw_response": None,
                "parsed_response": None,
                "used_model": model_name,
                "error": error_msg
            }
        else:
            return None

    try:
        # Build messages array
        conversation_messages = []

        # Add system prompt if provided
        if system_prompt:
            conversation_messages.append({"role": "system", "content": system_prompt})

        # Add previous messages if provided
        if messages:
            conversation_messages.extend(messages)

        # Add current user prompt
        conversation_messages.append({"role": "user", "content": prompt})

        response = client.chat.completions.create(
            model=model_name,
            messages=conversation_messages,
            temperature=temperature
        )

        raw_text = response.choices[0].message.content.strip()
        parsed_output = _parse_response(raw_text, formatter)

        if return_meta:
            return {
                "raw_response": raw_text,
                "parsed_response": parsed_output,
                "used_model": model_name,
                "error": None
            }
        else:
            return parsed_output

    except Exception as e:
        print(" Error during LLM response:", e)
        if return_meta:
            return {
                "raw_response": None,
                "parsed_response": None,
                "used_model": model_name,
                "error": str(e)
            }
        else:
            return None
