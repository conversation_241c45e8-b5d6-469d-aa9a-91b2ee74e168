# ai_utility_orchestrator/utils/toolkit.py

import os, json, logging, importlib
from typing import Dict, List, Callable, Any
from pathlib import Path

try:
    from importlib import resources
except ImportError:
    # Python < 3.9 fallback
    import importlib_resources as resources

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None

logger = logging.getLogger("ai_utility_orchestrator.toolkit")

class ConfigUtils:
    @staticmethod
    def _get_default_config() -> Dict[str, Any]:
        """Get the default configuration."""
        return {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
            "system_prompt": "You are an AI Orchestrator that helps users by routing queries to appropriate tools.",
            "default_user_id": "default_user",
            "context_limit": 3,
            "enable_parameter_enhancement": False,
            "enable_ai_response_generation": False,
            "context_storage": {"backend": "file", "file_path": None},
            "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
            "registry_config": {"registration_message": "✅ Registered tool: {tool_name}"},
            "tools": [],
            "output_format": "answer",
            "error_messages": {
                "tool_execution_failed": "I tried to use the {tool_name} tool, but encountered an error: {error}",
                "tool_not_found": "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}",
                "general_error": "I apologize, but I encountered an error while processing your request: {error}"
            },
            "default_responses": {
                "no_tool_needed": "I can help you with that, but I'm not sure how to respond."
            }
        }

    @staticmethod
    def load_config(file_path: str = None, overrides: dict = None, user_id: str = None) -> Dict[str, Any]:
        config = None
        config_path = None

        # Case 1: Load from specified file path
        if file_path:
            config_path = file_path
            if os.path.exists(file_path):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        config = json.load(f)
                except (json.JSONDecodeError, IOError) as e:
                    logger.error(f"Failed to load config from {file_path}: {e}")
                    config = None
            else:
                logger.error(f"Config file not found: {file_path}")
                config = None

        # Case 2: Load from package resources or fallback locations
        else:
            # Try package resources first
            try:
                config_files = resources.files('ai_utility_orchestrator') / 'config'
                config_content = (config_files / 'config_default.json').read_text(encoding='utf-8')
                config = json.loads(config_content)
                config_path = "package_resources"
            except (FileNotFoundError, ImportError, AttributeError, json.JSONDecodeError):
                # Fallback to file system search
                current_dir = Path.cwd()
                possible_paths = [
                    current_dir / "config" / "config_default.json",
                    current_dir / "config_default.json",
                    current_dir.parent / "config" / "config_default.json",
                ]

                # Add environment variable path if set
                env_config = os.getenv("AI_ORCHESTRATOR_CONFIG")
                if env_config:
                    possible_paths.insert(0, Path(env_config))

                for path in possible_paths:
                    if path.exists():
                        try:
                            with open(path, "r", encoding="utf-8") as f:
                                config = json.load(f)
                                config_path = str(path)
                                break
                        except (json.JSONDecodeError, IOError) as e:
                            logger.warning(f"Failed to load config from {path}: {e}")
                            continue

        # If no config was loaded, use default
        if config is None:
            logger.info("No config file found or loaded. Using default configuration.")
            config = ConfigUtils._get_default_config()

        # Apply user-specific config if available
        if user_id and config_path and config_path != "package_resources":
            try:
                user_config_path = Path(config_path).parent / f"user_{user_id}.json"
                if user_config_path.exists():
                    with open(user_config_path, "r", encoding="utf-8") as f:
                        user_config = json.load(f)
                        # Deep merge user config
                        ConfigUtils._deep_merge(config, user_config)
            except Exception as e:
                logger.warning(f"Could not load user config for {user_id}: {e}")

        # Apply overrides
        if overrides:
            ConfigUtils._deep_merge(config, overrides)

        return config

    @staticmethod
    def _deep_merge(base_dict: dict, update_dict: dict) -> None:
        """Deep merge update_dict into base_dict."""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                ConfigUtils._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value

    @staticmethod
    def load_tools_from_config(config: Dict[str, Any]) -> List[Dict[str, Any]]:
        return config.get("tools", [])

class EnvUtils:
    @staticmethod
    def get_env(key: str, default: str = "", required: bool = False) -> str:
        value = os.getenv(key, default)
        if required and not value:
            raise EnvironmentError(f"Missing required environment variable: {key}")
        return value

class LLMUtils:
    model_name = "gpt-4o-mini"
    temperature = 0.7

    @classmethod
    def set_model(cls, model, temp):
        cls.model_name = model
        cls.temperature = temp

    @classmethod
    def generate_response(cls, prompt: str) -> str:
        # Simulated LLM call (you can plug your real OpenAI or other LLM API here)
        return f"[{cls.model_name}@{cls.temperature}] → {prompt}"


class DynamicImportUtils:
    @staticmethod
    def load_object(path: str) -> Any:
        try:
            module_path, attr = path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            return getattr(module, attr)
        except Exception as e:
            logger.error(f"Could not import {path}: {e}")
            return None

class RegistryUtils:
    registered_tools: Dict[str, Callable] = {}

    @classmethod
    def register_tool(cls, name: str):
        def wrapper(func: Callable):
            cls.registered_tools[name] = func
            logger.info(f"Registered tool: {name}")
            return func
        return wrapper

    @classmethod
    def get_tool(cls, name: str) -> Callable:
        return cls.registered_tools.get(name)

    @classmethod
    def list_registered_tools(cls) -> List[str]:
        return list(cls.registered_tools.keys())
