#!/usr/bin/env python3
"""
Validation script to test if the configuration loading fix works.
Your friend can run this to verify the fix.
"""

import tempfile
import json
import os
import sys

def print_header(title):
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_test(test_name, status="RUNNING"):
    if status == "RUNNING":
        print(f"🔄 {test_name}...", end=" ", flush=True)
    elif status == "PASS":
        print("✅ PASSED")
    elif status == "FAIL":
        print("❌ FAILED")

def test_configuration_system():
    """Test the configuration system comprehensively."""
    print_test("Testing configuration system", "RUNNING")
    
    try:
        # Import the package
        from ai_utility_orchestrator import ConfigUtils
        
        # Test 1: Default config loading
        default_config = ConfigUtils.load_config()
        assert isinstance(default_config, dict), f"Expected dict, got {type(default_config)}"
        assert "llm" in default_config, "Missing 'llm' in default config"
        assert "system_prompt" in default_config, "Missing 'system_prompt' in default config"
        assert "tools" in default_config, "Missing 'tools' in default config"
        
        # Test 2: Custom config loading (the problematic test)
        test_config_data = {
            "test_setting": "test_value",
            "llm": {"model": "test-model", "temperature": 0.8},
            "custom_key": "custom_value"
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_config_data, f)
            temp_path = f.name
        
        try:
            # Verify file was created correctly
            assert os.path.exists(temp_path), f"Temp file not created: {temp_path}"
            
            # Load the custom config
            loaded_config = ConfigUtils.load_config(temp_path)
            assert isinstance(loaded_config, dict), f"Expected dict, got {type(loaded_config)}"
            
            # Check if custom values are preserved
            assert "test_setting" in loaded_config, f"Missing 'test_setting' in loaded config. Keys: {list(loaded_config.keys())}"
            assert loaded_config["test_setting"] == "test_value", f"Expected 'test_value', got '{loaded_config['test_setting']}'"
            
            assert "llm" in loaded_config, "Missing 'llm' in loaded config"
            assert "model" in loaded_config["llm"], "Missing 'model' in llm config"
            assert loaded_config["llm"]["model"] == "test-model", f"Expected 'test-model', got '{loaded_config['llm']['model']}'"
            
            assert "custom_key" in loaded_config, "Missing 'custom_key' in loaded config"
            assert loaded_config["custom_key"] == "custom_value", f"Expected 'custom_value', got '{loaded_config['custom_key']}'"
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
        
        # Test 3: Non-existent file (should fall back to defaults)
        nonexistent_config = ConfigUtils.load_config("/nonexistent/file.json")
        assert isinstance(nonexistent_config, dict), "Non-existent file should return default dict"
        assert "llm" in nonexistent_config, "Default fallback should have 'llm'"
        
        # Test 4: Invalid JSON file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content {")
            invalid_path = f.name
        
        try:
            invalid_config = ConfigUtils.load_config(invalid_path)
            assert isinstance(invalid_config, dict), "Invalid JSON should return default dict"
            assert "llm" in invalid_config, "Invalid JSON fallback should have 'llm'"
        finally:
            if os.path.exists(invalid_path):
                os.unlink(invalid_path)
        
        print_test("Testing configuration system", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing configuration system", "FAIL")
        print(f"   Error: {e}")
        print(f"   Error type: {type(e).__name__}")
        import traceback
        print("   Full traceback:")
        traceback.print_exc()
        return False

def main():
    """Run the configuration validation."""
    print_header("AI Utility Orchestrator - Configuration Fix Validation")
    print("This script tests if the configuration loading bug has been fixed.")
    
    # Test the configuration system
    success = test_configuration_system()
    
    # Print results
    print_header("Validation Results")
    if success:
        print("🎉 SUCCESS: Configuration system is working correctly!")
        print("\n✅ The configuration loading bug has been fixed!")
        print("✅ Custom config files are now loaded properly!")
        print("✅ The test should now pass on your friend's laptop!")
        
        print("\n📋 What was fixed:")
        print("   • Custom config files are now properly loaded")
        print("   • File existence is checked before loading")
        print("   • Better error handling for invalid JSON")
        print("   • Proper fallback to defaults when needed")
        print("   • Deep merging of configuration values")
        
        return 0
    else:
        print("❌ FAILURE: Configuration system still has issues!")
        print("\n🔧 The bug may not be fully fixed yet.")
        print("Please share this output for further debugging.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
